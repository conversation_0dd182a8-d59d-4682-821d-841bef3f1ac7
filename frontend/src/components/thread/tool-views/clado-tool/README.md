# Clado Tool View

A specialized tool view component for displaying LinkedIn research results from the Clado API integration.

## Features

### Supported Clado Functions

1. **search-linkedin-users** - Display LinkedIn profile search results
2. **search-linkedin-companies** - Display company search results  
3. **enrich-linkedin-profile** - Show detailed profile information
4. **get-linkedin-contacts** - Display contact information (emails, phones)
5. **scrape-linkedin-profile** - Show complete profile with experience, education, and posts
6. **get-linkedin-post-reactions** - Display post engagement data
7. **start-deep-research** - Show deep research job initiation
8. **get-deep-research-status** - Display research job progress and results

### UI Components

#### Profile Cards
- Profile pictures with fallback avatars
- Name, headline, and location display
- Expandable details with criteria matching
- LinkedIn profile links
- Website links

#### Company Cards  
- Company logos with fallback icons
- Industry and employee count
- YC batch information (for YC companies)
- Company websites and descriptions
- Specialties and criteria matching

#### Contact Information
- Email and phone display with icons
- Confidence ratings
- Social media links

#### Experience & Education
- Company/school logos
- Date ranges with "Present" handling
- Descriptions and locations
- Timeline formatting

#### Post Reactions
- User avatars and names
- Reaction types (LIKE, LOVE, etc.)
- Pagination support
- Profile links

#### Deep Research Jobs
- Job status indicators
- Progress tracking with batch information
- Results preview (first 5 profiles)
- Enrichment statistics
- Error handling

### Design Features

- **LinkedIn Branding** - Blue color scheme with LinkedIn icon
- **Dark Mode Support** - Responsive to theme changes
- **Responsive Layout** - Works on all screen sizes
- **Loading States** - Animated loading for streaming responses
- **Expandable Content** - Collapsible profile details
- **Cost Tracking** - Display API credit usage
- **Timestamps** - Show when operations completed

### Data Handling

- **Flexible Parsing** - Handles multiple data formats
- **Tool Detection** - Automatically identifies Clado function type
- **Error Resilience** - Graceful handling of missing data
- **Type Safety** - Full TypeScript interfaces

## Usage

The component is automatically registered for all Clado tool functions:

```typescript
// Automatically used for these tool names:
'search-linkedin-users'
'search-linkedin-companies' 
'enrich-linkedin-profile'
'get-linkedin-contacts'
'scrape-linkedin-profile'
'get-linkedin-post-reactions'
'start-deep-research'
'get-deep-research-status'
```

## File Structure

```
clado-tool/
├── CladoToolView.tsx     # Main component
├── _utils.ts             # Data extraction utilities
└── README.md             # This documentation
```

## Dependencies

- React hooks (useState)
- Lucide React icons
- UI components (Card, Badge, ScrollArea, Progress)
- Theme support (useTheme)
- Utility functions from parent tool views

## Future Enhancements

- WebSocket support for real-time search
- Export functionality for results
- Advanced filtering and sorting
- Bulk operations support
- Integration with CRM systems
